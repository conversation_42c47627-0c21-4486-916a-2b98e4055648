namespace MyERP.Models
{
    public class SalesOrderItem
    {
        public int Id { get; set; }
        public int SalesOrderId { get; set; }
        public int ProductId { get; set; }
        public decimal Quantity { get; set; }
        public decimal UnitPrice { get; set; }
        public decimal Discount { get; set; }
        public decimal TotalPrice { get; set; }
        
        // Navigation properties
        public SalesOrder SalesOrder { get; set; } = null!;
        public Product Product { get; set; } = null!;
    }
}