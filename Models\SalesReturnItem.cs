namespace MyERP.Models
{
    public class SalesReturnItem
    {
        public int Id { get; set; }
        public int SalesReturnId { get; set; }
        public int ProductId { get; set; }
        public decimal Quantity { get; set; }
        public decimal UnitPrice { get; set; }
        public decimal TotalPrice { get; set; }
        public string Condition { get; set; } = string.Empty; // Defective, Expired, Damaged
        
        // Navigation properties
        public SalesReturn SalesReturn { get; set; } = null!;
        public Product Product { get; set; } = null!;
    }
}