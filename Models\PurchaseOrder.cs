namespace MyERP.Models
{
    public class PurchaseOrder
    {
        public int Id { get; set; }
        public string PONumber { get; set; } = string.Empty;
        public DateTime OrderDate { get; set; }
        public DateTime ExpectedDeliveryDate { get; set; }
        public int VendorId { get; set; }
        public decimal TotalAmount { get; set; }
        public string Status { get; set; } = string.Empty; // Draft, Approved, Received, Cancelled
        public string Notes { get; set; } = string.Empty;
        
        // Navigation properties
        public Vendor Vendor { get; set; } = null!;
        public ICollection<PurchaseOrderItem> Items { get; set; } = new List<PurchaseOrderItem>();
        public ICollection<GoodsReceiptNote> ReceiptNotes { get; set; } = new List<GoodsReceiptNote>();
    }
}