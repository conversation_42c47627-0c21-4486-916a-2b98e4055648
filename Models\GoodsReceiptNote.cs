namespace MyERP.Models
{
    public class GoodsReceiptNote
    {
        public int Id { get; set; }
        public string GRNNumber { get; set; } = string.Empty;
        public int PurchaseOrderId { get; set; }
        public DateTime ReceiptDate { get; set; }
        public string ReceivedBy { get; set; } = string.Empty;
        public string Status { get; set; } = string.Empty; // Pending, Inspected, Accepted, Rejected
        public string Notes { get; set; } = string.Empty;
        
        // Navigation properties
        public PurchaseOrder PurchaseOrder { get; set; } = null!;
        public ICollection<GoodsReceiptItem> Items { get; set; } = new List<GoodsReceiptItem>();
    }
}