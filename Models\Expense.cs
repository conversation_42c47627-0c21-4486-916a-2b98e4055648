namespace MyERP.Models
{
    public class Expense
    {
        public int Id { get; set; }
        public string ExpenseType { get; set; } = string.Empty; // Utility, Maintenance, Rent, etc.
        public string Description { get; set; } = string.Empty;
        public decimal Amount { get; set; }
        public DateTime ExpenseDate { get; set; }
        public string PaidBy { get; set; } = string.Empty;
        public string PaymentMethod { get; set; } = string.Empty;
        public string ReferenceNumber { get; set; } = string.Empty;
        public string