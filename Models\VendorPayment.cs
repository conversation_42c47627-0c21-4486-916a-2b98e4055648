namespace MyERP.Models
{
    public class VendorPayment
    {
        public int Id { get; set; }
        public int VendorId { get; set; }
        public string InvoiceNumber { get; set; } = string.Empty;
        public DateTime InvoiceDate { get; set; }
        public DateTime DueDate { get; set; }
        public decimal Amount { get; set; }
        public decimal PaidAmount { get; set; }
        public string PaymentStatus { get; set; } = string.Empty; // Pending, Partial, Paid
        public string PaymentMethod { get; set; } = string.Empty; // Bank Transfer, Check, Cash
        public DateTime? PaymentDate { get; set; }
        public string ReferenceNumber { get; set; } = string.Empty;
        
        // Navigation properties
        public Vendor Vendor { get; set; } = null!;
    }
}