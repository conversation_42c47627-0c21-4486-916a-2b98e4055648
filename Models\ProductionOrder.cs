namespace MyERP.Models
{
    public class ProductionOrder
    {
        public int Id { get; set; }
        public string OrderNumber { get; set; } = string.Empty;
        public int ProductId { get; set; }
        public decimal QuantityToProduct { get; set; }
        public decimal QuantityProduced { get; set; }
        public DateTime PlannedStartDate { get; set; }
        public DateTime? ActualStartDate { get; set; }
        public DateTime? CompletionDate { get; set; }
        public string Status { get; set; } = string.Empty; // Planned, In Progress, Completed, Cancelled
        public decimal ProductionCost { get; set; }
        public string BatchNumber { get; set; } = string.Empty;
        
        // Navigation properties
        public Product Product { get; set; } = null!;
        public ICollection<ProductionCost> Costs { get; set; } = new List<ProductionCost>();
    }
}