namespace MyERP.Models
{
    public class RawMaterial
    {
        public int Id { get; set; }
        public string Name { get; set; } = string.Empty;
        public string Code { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
        public string UnitOfMeasure { get; set; } = string.Empty;
        public decimal CurrentStock { get; set; }
        public decimal MinimumStock { get; set; }
        public decimal ReorderPoint { get; set; }
        public decimal UnitCost { get; set; }
        
        // Navigation properties
        public ICollection<PurchaseOrderItem> PurchaseOrderItems { get; set; } = new List<PurchaseOrderItem>();
        public ICollection<ProductionMaterial> ProductionMaterials { get; set; } = new List<ProductionMaterial>();
    }
}