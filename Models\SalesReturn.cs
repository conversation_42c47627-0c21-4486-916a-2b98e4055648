namespace MyERP.Models
{
    public class SalesReturn
    {
        public int Id { get; set; }
        public string ReturnNumber { get; set; } = string.Empty;
        public int SalesOrderId { get; set; }
        public DateTime ReturnDate { get; set; }
        public string ReturnReason { get; set; } = string.Empty; // Defect, Expiry, Damage
        public decimal TotalAmount { get; set; }
        public string Status { get; set; } = string.Empty; // Pending, Inspected, Approved, Rejected
        public string Notes { get; set; } = string.Empty;
        
        // Navigation properties
        public SalesOrder SalesOrder { get; set; } = null!;
        public ICollection<SalesReturnItem> Items { get; set; } = new List<SalesReturnItem>();
    }
}