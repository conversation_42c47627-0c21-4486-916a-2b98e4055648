namespace MyERP.Models
{
    public class SalesOrder
    {
        public int Id { get; set; }
        public string OrderNumber { get; set; } = string.Empty;
        public int CustomerId { get; set; }
        public DateTime OrderDate { get; set; }
        public DateTime? DeliveryDate { get; set; }
        public decimal TotalAmount { get; set; }
        public decimal DiscountAmount { get; set; }
        public decimal NetAmount { get; set; }
        public string Status { get; set; } = string.Empty; // Draft, Confirmed, Shipped, Delivered, Cancelled
        public string Notes { get; set; } = string.Empty;
        
        // Navigation properties
        public Customer Customer { get; set; } = null!;
        public ICollection<SalesOrderItem> Items { get; set; } = new List<SalesOrderItem>();
        public ICollection<SalesReturn> Returns { get; set; } = new List<SalesReturn>();
    }
}