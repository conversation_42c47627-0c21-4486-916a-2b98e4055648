namespace MyERP.Models
{
    public class GoodsReceiptItem
    {
        public int Id { get; set; }
        public int GoodsReceiptNoteId { get; set; }
        public int RawMaterialId { get; set; }
        public decimal ReceivedQuantity { get; set; }
        public DateTime? ExpiryDate { get; set; }
        public string BatchNumber { get; set; } = string.Empty;
        public string QualityStatus { get; set; } = string.Empty; // Passed, Failed, Pending
        
        // Navigation properties
        public GoodsReceiptNote GoodsReceiptNote { get; set; } = null!;
        public RawMaterial RawMaterial { get; set; } = null!;
    }
}