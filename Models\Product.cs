namespace MyERP.Models
{
    public class Product
    {
        public int Id { get; set; }
        public string Name { get; set; } = string.Empty;
        public string Code { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
        public string Category { get; set; } = string.Empty;
        public string UnitOfMeasure { get; set; } = string.Empty;
        public decimal SellingPrice { get; set; }
        public decimal CurrentStock { get; set; }
        public decimal MinimumStock { get; set; }
        public int ShelfLifeDays { get; set; }
        
        // Navigation properties
        public ICollection<ProductionMaterial> Materials { get; set; } = new List<ProductionMaterial>();
        public ICollection<ProductionOrder> ProductionOrders { get; set; } = new List<ProductionOrder>();
        public ICollection<SalesOrderItem> SalesOrderItems { get; set; } = new List<SalesOrderItem>();
    }
}