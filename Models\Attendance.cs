namespace MyERP.Models
{
    public class Attendance
    {
        public int Id { get; set; }
        public int EmployeeId { get; set; }
        public DateTime Date { get; set; }
        public DateTime? TimeIn { get; set; }
        public DateTime? TimeOut { get; set; }
        public string Status { get; set; } = string.Empty; // Present, Absent, Leave, Half-day
        public string Notes { get; set; } = string.Empty;
        
        // Navigation properties
        public Employee Employee { get; set; } = null!;
    }
}