namespace MyERP.Models
{
    public class ProductionCost
    {
        public int Id { get; set; }
        public int ProductionOrderId { get; set; }
        public string CostType { get; set; } = string.Empty; // Direct Labor, Overhead, Waste
        public string Description { get; set; } = string.Empty;
        public decimal Amount { get; set; }
        
        // Navigation properties
        public ProductionOrder ProductionOrder { get; set; } = null!;
    }
}